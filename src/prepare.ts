import { parseArgs } from 'node:util';
import { config } from 'dotenv';
import { JiraClient } from './jira.js';

// Load environment variables from .env file
config();

interface CliArgs {
  issueId?: string;
  help?: boolean;
}

function parseCliArgs(): CliArgs {
  try {
    const { values } = parseArgs({
      args: process.argv.slice(2),
      options: {
        'issue-id': {
          type: 'string',
          short: 'i',
        },
        help: {
          type: 'boolean',
          short: 'h',
        },
      },
    });

    return {
      issueId: values['issue-id'] as string,
      help: values.help as boolean,
    };
  } catch (error) {
    console.error('Error parsing arguments:', error);
    process.exit(1);
  }
}

function showHelp(): void {
  console.log(`
Usage: tsx src/prepare.ts --issue-id <JIRA_ISSUE_ID>

Options:
  -i, --issue-id <ID>    JIRA issue ID (e.g., SWS-34515)
  -h, --help            Show this help message

Examples:
  tsx src/prepare.ts --issue-id SWS-34515
  tsx src/prepare.ts -i SWS-34515
`);
}

async function run(): Promise<void> {
  const args = parseCliArgs();
  if (args.help) {
    showHelp();
    return;
  }

  if (!args.issueId) {
    console.error('Error: JIRA issue ID is required');
    showHelp();
    process.exit(1);
  }

  const jiraClient = new JiraClient({
    serverUrl: process.env.JIRA_SERVER_URL,
    credentials: process.env.JIRA_CREDENTIALS,
  });

  if (!jiraClient.validateJiraIssueId(args.issueId)) {
    console.error(`Error: Invalid JIRA issue ID format: '${args.issueId}'`);
    console.error('Expected format: PROJECT-NUMBER (e.g., SWS-34515)');
    process.exit(1);
  }

  console.log(`Processing JIRA issue: ${args.issueId}`);

  // Step 1: Create initial tracking comment
  await jiraClient.comments(args.issueId).post('is working…');
}

run().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
